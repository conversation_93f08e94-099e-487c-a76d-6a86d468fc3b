# 数据库表结构修改计划_20250731104447

## 项目概述
- **项目名称**: SOC应答系统数据库设计优化
- **执行时间**: 2025年07月31日 10:44:47
- **主要目标**: 
  1. 条目产品关联表修改为条目应答表
  2. 按照表结构更新ER图
  3. 添加权限管理表，确定条目应答表哪些人能看-多选

## 第一性原理分析
### 业务本质分析
- **核心业务**: SOC应答是对标书条目的逐点回应过程
- **关键实体**: 条目(Item) + 应答(Response) 是业务核心，而非条目与产品的简单关联
- **权限需求**: 不同角色对应答内容有不同的查看权限，需要细粒度权限控制

### 设计原则
1. **语义准确性**: 表名应准确反映业务含义
2. **权限分离**: 权限管理独立设计，支持灵活配置
3. **扩展性**: 设计应支持未来业务扩展需求

## 执行任务列表

### 任务1: 分析现有表结构
- **状态**: 已完成
- **内容**: 
  - 已读取当前数据库设计文档
  - 分析了soc_item_product表的现有结构
  - 理解了表间关系和业务逻辑

### 任务2: 重构条目产品关联表为条目应答表
- **状态**: 待执行
- **详细步骤**:
  1. 将表名从`soc_item_product`修改为`soc_item_response`
  2. 优化字段命名，使其更符合应答业务语义
  3. 调整注释，明确表达应答业务含义
  4. 保持现有业务逻辑不变

### 任务3: 设计权限管理表
- **状态**: 待执行
- **详细步骤**:
  1. 设计用户角色表 `soc_role`
  2. 设计用户角色关联表 `soc_user_role`
  3. 设计条目应答权限表 `soc_item_response_permission`
  4. 支持多选用户/角色对条目应答的查看权限

### 任务4: 更新ER图
- **状态**: 待执行
- **详细步骤**:
  1. 更新表名和字段名称
  2. 添加权限管理相关表的关系
  3. 更新表注释和关系说明
  4. 确保ER图的完整性和准确性

### 任务5: 更新相关外键约束
- **状态**: 待执行
- **详细步骤**:
  1. 更新涉及条目产品关联表的外键约束
  2. 添加权限管理表的外键约束
  3. 确保数据完整性

### 任务6: 文档整理
- **状态**: 待执行
- **详细步骤**:
  1. 更新数据库设计文档
  2. 确保所有表结构、注释、关系图的一致性
  3. 添加权限管理相关的设计说明

## 风险评估
1. **兼容性风险**: 表名变更可能影响现有代码，需要确保向下兼容
2. **性能风险**: 新增权限表可能影响查询性能，需要合理设计索引
3. **复杂性风险**: 权限管理增加了系统复杂性，需要简化设计

## 预期收益
1. **语义清晰**: 表名更准确地反映业务含义
2. **权限简化**: 简化权限控制，直接基于用户设置权限，支持只读和可编辑两种类型
3. **操作便捷**: 移除复杂的角色管理，直接为用户分配权限，操作更简单

## 验收标准
1. 表名准确反映业务语义
2. 权限管理功能简化且可用
3. ER图关系正确完整
4. 所有外键约束正确
5. 文档更新完整准确

## 权限表简化说明
根据用户反馈，权限表设计过于复杂，已简化为：
- 移除角色表 (soc_role)
- 移除用户角色关联表 (soc_user_role)
- 简化权限表 (soc_item_response_permission)，只包含：
  - 用户工号 (user_emp_no)
  - 用户姓名 (user_name)  
  - 权限类型 (permission_action): READ_ONLY-只读, EDITABLE-可编辑
  - 条目应答ID (item_response_id)