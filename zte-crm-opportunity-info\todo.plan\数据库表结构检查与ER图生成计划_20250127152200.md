# 数据库表结构检查与ER图生成计划

**时间**: 2025年01月27日 15:22:00
**任务**: 检查数据库表格式、逻辑并重新生成ER图

## 执行计划

### 1. 表结构检查阶段 (10分钟)
- [x] 检查所有表的字段定义一致性
- [x] 验证索引字段是否存在
- [x] 检查外键关系的合理性
- [x] 验证数据类型的适当性

### 2. 问题修复阶段 (15分钟)
- [ ] 修复发现的字段不匹配问题
- [ ] 完善不完整的表结构定义
- [ ] 调整不合理的数据类型和长度
- [ ] 优化索引设计

### 3. ER图生成阶段 (20分钟)
- [ ] 基于修正后的表结构生成ER图
- [ ] 标注表间关系和外键约束
- [ ] 添加业务逻辑说明
- [ ] 验证ER图的完整性

## 发现的问题清单

### 1. 索引字段不匹配
- `soc_item_response`表中有`idx_product_code`索引，但字段应为`business_code`

### 2. 表结构不完整
- `soc_item_response_version`表结构定义不完整

### 3. 字段命名一致性
- 需要检查各表间关联字段的命名是否一致

## 预期输出
1. 修正后的完整数据库表结构文档
2. 标准的ER图（使用Mermaid格式）
3. 表关系说明文档