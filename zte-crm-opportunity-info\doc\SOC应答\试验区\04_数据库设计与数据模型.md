
# 1. 数据库设计与数据模型（ER图）



**设计理念**:
- 以 `soc_item_response`（条目应答）为核心业务实体
- 所有扩展功能（标签、历史、AI匹配）都基于此实体进行关联
- 确保数据一致性和业务逻辑的完整性

### 1.1 核心业务表
- **任务表(soc_task)**：SOC应答任务管理
- **条目表(soc_item)**：应答条目信息管理
- **条目应答表(soc_item_response)**：条目应答信息管理

### 1.2 扩展功能表
- **标签表(soc_tag)**：标签字典管理
- **条目标签关联表(soc_item_tag)**：条目应答标签关系
- **AI匹配结果表(soc_ai_match_result)**：AI智能匹配结果
- **历史版本表(soc_item_history)**：变更历史记录
- **权限管理表(soc_item_response_permission)**：简化的用户权限控制

## 1.3 核心表设计

### 1.3.1 任务表 (soc_task)

**表结构设计**:
```sql
CREATE TABLE soc_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码，格式：TASK001',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS,文档库,项目文档,历史SOC文档',
    attachment_file_path VARCHAR(500) COMMENT '应答条目文件路径',
    task_status VARCHAR(20) DEFAULT '未开始' COMMENT '任务状态：未开始-NOT_STARTED,进行中-IN_PROGRESS,已完成-COMPLETED',
    is_personal TINYINT(1) DEFAULT 0 COMMENT '是否为个人任务：0-否,1-是',
    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    UNIQUE KEY uk_task_code (task_code),
    INDEX idx_task_status (task_status),
    INDEX idx_created_by (created_by),
    INDEX idx_enabled_flag (enabled_flag)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答任务表';
```



### 1.3.2 条目表 (soc_item)

**表结构设计**:
```sql
CREATE TABLE soc_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',


    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_task_id (task_id),
    INDEX idx_item_code (item_code),
    INDEX idx_enabled_flag (enabled_flag),
    FULLTEXT INDEX idx_description_fulltext (item_description),
    UNIQUE KEY uk_task_item_code (task_id, item_code, enabled_flag)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答条目表';
```

### 1.3.3 条目应答表 (soc_item_response)

**表结构设计**:
```sql
CREATE TABLE soc_item_response (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型：产品/服务/解决方案/其他',
    business_code VARCHAR(200) NOT NULL COMMENT '业务编码',
    business_name VARCHAR(200) NOT NULL COMMENT '业务名称',
    response_status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态：NOT_ANSWERED-未应答,ANSWERING-应答中,ANSWERED-已应答',
    assign_emp VARCHAR(100) COMMENT '指派给（姓名+工号）',
    assign_emp_no VARCHAR(50) COMMENT '指派给用户empNo',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_id (item_id),
    INDEX idx_task_id (task_id),
    INDEX idx_business_code (business_code),
    INDEX idx_response_status (response_status),
    INDEX idx_assign_emp_no (assign_emp_no),
    INDEX idx_enabled_flag (enabled_flag)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目应答表';
```


## 1.4 扩展表设计

### 1.4.1 标签相关表

**标签字典表 (soc_tag)**:
```sql
CREATE TABLE soc_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    UNIQUE KEY uk_tag_name (tag_name),
    INDEX idx_enabled_flag (enabled_flag)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签字典表';
```

**条目标签关联表 (soc_item_tag)**:
```sql
CREATE TABLE soc_item_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_response_id BIGINT NOT NULL COMMENT '条目应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID（冗余字段，便于查询）',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称（冗余字段）',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_response_id (item_response_id),
    INDEX idx_tag_id (tag_id),
    UNIQUE KEY uk_item_response_tag (item_response_id, tag_id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目应答标签关联表';
```

### 1.4.2 AI匹配结果表

**AI匹配结果表 (soc_ai_match_result)**:
```sql
CREATE TABLE soc_ai_match_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配结果ID',
    item_response_id BIGINT NOT NULL COMMENT '条目应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源：GBBS,文档库等',
    source_id VARCHAR(100) COMMENT '数据源中的记录ID',
    source_description TEXT COMMENT '数据源条目描述',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度分数',
    semantic_score DECIMAL(5,2) COMMENT '语义相似度分数',
    context_score DECIMAL(5,2) COMMENT '上下文匹配分数',
    country_match TINYINT(1) DEFAULT 0 COMMENT '国家匹配：0-否,1-是',
    branch_match TINYINT(1) DEFAULT 0 COMMENT '分支匹配：0-否,1-是',
    customer_match TINYINT(1) DEFAULT 0 COMMENT '客户匹配：0-否,1-是',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_content LONGTEXT COMMENT '应答说明',
    source_index VARCHAR(200) COMMENT '索引链接',
    is_applied TINYINT(1) DEFAULT 0 COMMENT '是否已应用：0-否,1-是',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_response_id (item_response_id),
    INDEX idx_match_score (match_score DESC),
    INDEX idx_is_applied (is_applied),
    INDEX idx_enabled_flag (enabled_flag)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配结果表';
```

### 1.4.3 历史版本表

**历史版本表 (soc_item_response_version)**:
```sql
CREATE TABLE soc_item_response_version (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史版本ID',
    item_response_id BIGINT NOT NULL COMMENT '条目应答ID',
    version_num INT NOT NULL COMMENT '版本号',
    is_applied TINYINT(1) DEFAULT 0 COMMENT '是否已应用：0-否,1-是',
    response_method VARCHAR(20) COMMENT '应答方式：AI-AI应答,MANUAL-手工应答',
    additional_info TEXT COMMENT '补充信息',
    satisfaction VARCHAR(10) COMMENT '满足度：FC-完全满足,PC-部分满足,NC-不满足',
    response_content LONGTEXT COMMENT '应答说明（支持富文本）',
    source_index VARCHAR(200) COMMENT '索引链接',
    source VARCHAR(50) COMMENT '应答来源：GBBS,文档库等',
    remark TEXT COMMENT '备注',

    match_score DECIMAL(5,2) COMMENT '匹配度分数',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_response_id (item_response_id),
    INDEX idx_version_num (version_num),
    INDEX idx_enabled_flag (enabled_flag),
    UNIQUE KEY uk_response_version (item_response_id, version_num)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目应答历史版本表（soc_item_response的镜像表）';
```

### 1.4.4 权限管理表

**条目应答权限表 (soc_item_response_permission)**:
```sql
CREATE TABLE soc_item_response_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    item_response_id BIGINT NOT NULL COMMENT '条目应答ID',
    user_emp_no VARCHAR(50) NOT NULL COMMENT '用户工号',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    permission_action VARCHAR(20) NOT NULL COMMENT '权限类型：READ_ONLY-只读,EDITABLE-可编辑',

    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    last_updated_by VARCHAR(50) NOT NULL COMMENT '最后更新人工号',
    last_updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '最后更新时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID',

    -- 索引设计
    INDEX idx_item_response_id (item_response_id),
    INDEX idx_user_emp_no (user_emp_no),
    INDEX idx_enabled_flag (enabled_flag),
    -- 组合索引，快速查询用户对条目应答的权限
    UNIQUE KEY uk_user_response_permission (item_response_id, user_emp_no, enabled_flag),
    INDEX idx_composite_permission (user_emp_no, permission_action)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目应答权限表';
```

## 1.5 数据库ER图

### 1.5.1 完整ER图
基于上述表结构设计，系统的完整实体关系图如下：
