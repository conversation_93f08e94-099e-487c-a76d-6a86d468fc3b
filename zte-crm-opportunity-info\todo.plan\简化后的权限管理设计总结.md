# 简化后的权限管理设计总结

## 修改概述
根据用户反馈，原权限表设计过于复杂，已按照要求简化为直接的用户权限控制。

## 主要变更

### 1. 移除的表
- `soc_role` (角色表)
- `soc_user_role` (用户角色关联表)

### 2. 简化的权限表: soc_item_response_permission

**核心字段**:
```sql
- id: 权限ID (主键)
- item_response_id: 条目应答ID (外键)
- user_emp_no: 用户工号
- user_name: 用户姓名  
- permission_action: 权限类型 (READ_ONLY-只读, EDITABLE-可编辑)
```

**特点**:
- 直接管理用户对条目应答的权限
- 支持多个用户对同一条目应答设置不同权限（多选）
- 权限类型简化为只读和可编辑两种
- 通过唯一约束确保同一用户对同一条目应答只有一个权限记录

## 权限控制逻辑

### 权限类型
1. **READ_ONLY**: 只读权限，用户只能查看条目应答内容
2. **EDITABLE**: 可编辑权限，用户可以查看和编辑条目应答内容

### 多选权限实现
- 一个条目应答可以设置多个用户的权限
- 每个用户对每个条目应答只能有一种权限类型
- 通过权限表的记录来控制用户访问

### 权限查询示例
```sql
-- 查询用户对特定条目应答的权限
SELECT permission_action 
FROM soc_item_response_permission 
WHERE item_response_id = ? AND user_emp_no = ? AND enabled_flag = 'Y';

-- 查询条目应答的所有授权用户
SELECT user_emp_no, user_name, permission_action 
FROM soc_item_response_permission 
WHERE item_response_id = ? AND enabled_flag = 'Y';
```

## 索引设计
- 主键索引 (id)
- 条目应答ID索引 (item_response_id)
- 用户工号索引 (user_emp_no)
- 唯一约束 (item_response_id, user_emp_no, enabled_flag)
- 复合索引 (user_emp_no, permission_action)

## 优势
1. **简单明了**: 去除角色复杂性，直接管理用户权限
2. **高效查询**: 减少表关联，提高权限验证效率
3. **灵活配置**: 支持对每个条目应答精确控制权限
4. **多选支持**: 满足多人协作的权限需求

## 使用场景
- 项目经理为特定条目应答分配可编辑权限给相关负责人
- 其他相关人员设置为只读权限，可以查看但不能修改
- 支持权限的动态调整，满足项目进展的需要